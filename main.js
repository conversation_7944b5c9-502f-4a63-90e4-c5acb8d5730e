const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const Database = require('better-sqlite3');

// Setup DB path in userData dir
const dbPath = path.join(app.getPath('userData'), 'tasks.sqlite3');
let db;

// iCloud Drive setup
const iCloudPath = path.join(os.homedir(), 'Library', 'Mobile Documents', 'com~apple~CloudDocs', 'RedTasks');
const iCloudDataFile = path.join(iCloudPath, 'tasks.json');
const iCloudMetaFile = path.join(iCloudPath, 'sync-meta.json');

// Ensure iCloud directory exists
function ensureiCloudDirectory() {
  try {
    if (!fs.existsSync(iCloudPath)) {
      fs.mkdirSync(iCloudPath, { recursive: true });
      console.log('Created iCloud directory:', iCloudPath);
    }
    return true;
  } catch (error) {
    console.error('Failed to create iCloud directory:', error);
    return false;
  }
}

// iCloud sync functions
function loadTasksFromiCloud() {
  try {
    if (!fs.existsSync(iCloudDataFile)) {
      console.log('No iCloud data file found, starting fresh');
      return [];
    }

    const data = fs.readFileSync(iCloudDataFile, 'utf8');
    const tasks = JSON.parse(data);
    console.log(`Loaded ${tasks.length} tasks from iCloud`);
    return tasks;
  } catch (error) {
    console.error('Failed to load tasks from iCloud:', error);
    return [];
  }
}

function saveTasksToiCloud(tasks) {
  try {
    if (!ensureiCloudDirectory()) {
      return false;
    }

    // Save tasks data
    fs.writeFileSync(iCloudDataFile, JSON.stringify(tasks, null, 2));

    // Save sync metadata
    const syncMeta = {
      lastSync: new Date().toISOString(),
      taskCount: tasks.length,
      version: '1.0'
    };
    fs.writeFileSync(iCloudMetaFile, JSON.stringify(syncMeta, null, 2));

    console.log(`Saved ${tasks.length} tasks to iCloud`);
    return true;
  } catch (error) {
    console.error('Failed to save tasks to iCloud:', error);
    return false;
  }
}

function getSyncMetadata() {
  try {
    if (!fs.existsSync(iCloudMetaFile)) {
      return null;
    }
    const data = fs.readFileSync(iCloudMetaFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Failed to read sync metadata:', error);
    return null;
  }
}

// Helper functions to convert between SQLite and JSON formats
function convertSQLiteTaskToJSON(sqliteTask) {
  return {
    id: sqliteTask.id,
    description: sqliteTask.name,
    tags: sqliteTask.tags || [],
    deadline: sqliteTask.date,
    priority: sqliteTask.type,
    notes: sqliteTask.notes || ''
  };
}

function convertJSONTaskToSQLite(jsonTask) {
  return {
    id: jsonTask.id,
    name: jsonTask.description,
    date: jsonTask.deadline,
    type: jsonTask.priority,
    notes: jsonTask.notes || '',
    tags: jsonTask.tags || []
  };
}

// Sync functions
function syncFromiCloudToSQLite() {
  try {
    const iCloudTasks = loadTasksFromiCloud();
    if (iCloudTasks.length === 0) {
      console.log('No tasks to sync from iCloud');
      return true;
    }

    // Clear existing data
    db.prepare('DELETE FROM task_tags').run();
    db.prepare('DELETE FROM tasks').run();
    db.prepare('DELETE FROM tags').run();

    // Insert tasks from iCloud
    for (const jsonTask of iCloudTasks) {
      const sqliteTask = convertJSONTaskToSQLite(jsonTask);
      const info = db.prepare('INSERT INTO tasks (id, name, date, type, notes) VALUES (?, ?, ?, ?, ?)').run(
        sqliteTask.id, sqliteTask.name, sqliteTask.date, sqliteTask.type, sqliteTask.notes
      );

      // Insert tags
      if (Array.isArray(sqliteTask.tags)) {
        for (const tagName of sqliteTask.tags) {
          let tagId;
          try {
            const tagInfo = db.prepare('INSERT INTO tags (name) VALUES (?)').run(tagName);
            tagId = tagInfo.lastInsertRowid;
          } catch {
            tagId = db.prepare('SELECT id FROM tags WHERE name = ?').get(tagName)?.id;
          }
          if (tagId) {
            db.prepare('INSERT OR IGNORE INTO task_tags (task_id, tag_id) VALUES (?, ?)').run(sqliteTask.id, tagId);
          }
        }
      }
    }

    console.log(`Synced ${iCloudTasks.length} tasks from iCloud to SQLite`);
    return true;
  } catch (error) {
    console.error('Failed to sync from iCloud to SQLite:', error);
    return false;
  }
}

function syncFromSQLiteToiCloud() {
  try {
    // Get all tasks from SQLite
    const tasks = db.prepare('SELECT * FROM tasks').all();
    for (const task of tasks) {
      const tagRows = db.prepare(`SELECT tags.name FROM tags
        JOIN task_tags ON tags.id = task_tags.tag_id WHERE task_tags.task_id = ?`).all(task.id);
      task.tags = tagRows.map(r => r.name);
    }

    // Convert to JSON format
    const jsonTasks = tasks.map(convertSQLiteTaskToJSON);

    // Save to iCloud
    return saveTasksToiCloud(jsonTasks);
  } catch (error) {
    console.error('Failed to sync from SQLite to iCloud:', error);
    return false;
  }
}

function initDatabase() {
  db = new Database(dbPath);
  db.pragma('journal_mode = WAL');
  // Tabella task
  db.prepare(`CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    date TEXT,
    type TEXT NOT NULL,
    notes TEXT
  )`).run();

  // Migrazione: aggiungi colonna notes se non esiste
  try {
    db.prepare(`ALTER TABLE tasks ADD COLUMN notes TEXT`).run();
    console.log('Added notes column to tasks table');
  } catch (error) {
    // Column already exists, ignore error
  }

  // Tabella tag
  db.prepare(`CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE
  )`).run();
  // Tabella relazione task-tag
  db.prepare(`CREATE TABLE IF NOT EXISTS task_tags (
    task_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (task_id, tag_id),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
  )`).run();
}

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    }
  });
  win.loadFile(path.join(__dirname, 'public', 'index.html'));
  win.webContents.openDevTools();
}

app.whenReady().then(() => {
  initDatabase();

  // Initialize iCloud sync
  console.log('Initializing iCloud sync...');
  if (ensureiCloudDirectory()) {
    // Load tasks from iCloud on startup
    syncFromiCloudToSQLite();
  }

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC API
ipcMain.handle('getTasks', () => {
  const tasks = db.prepare('SELECT * FROM tasks').all();
  for (const task of tasks) {
    const tagRows = db.prepare(`SELECT tags.name FROM tags
      JOIN task_tags ON tags.id = task_tags.tag_id WHERE task_tags.task_id = ?`).all(task.id);
    task.tags = tagRows.map(r => r.name);
  }

  // Convert SQLite format to JSON format for frontend
  const jsonTasks = tasks.map(convertSQLiteTaskToJSON);
  console.log('Returning tasks to frontend:', jsonTasks.length);
  return jsonTasks;
});
ipcMain.handle('addTask', (event, task) => {
  const info = db.prepare('INSERT INTO tasks (name, date, type, notes) VALUES (?, ?, ?, ?)').run(task.name, task.date, task.type, task.notes || '');
  const taskId = info.lastInsertRowid;
  if (Array.isArray(task.tags)) {
    for (const tagName of task.tags) {
      let tagId;
      try {
        const tagInfo = db.prepare('INSERT INTO tags (name) VALUES (?)').run(tagName);
        tagId = tagInfo.lastInsertRowid;
      } catch {
        tagId = db.prepare('SELECT id FROM tags WHERE name = ?').get(tagName)?.id;
      }
      if (tagId) {
        db.prepare('INSERT OR IGNORE INTO task_tags (task_id, tag_id) VALUES (?, ?)').run(taskId, tagId);
      }
    }
  }

  // Sync to iCloud after adding task
  syncFromSQLiteToiCloud();

  return taskId;
});
ipcMain.handle('deleteTask', (event, taskId) => {
  db.prepare('DELETE FROM tasks WHERE id = ?').run(taskId);
  db.prepare('DELETE FROM task_tags WHERE task_id = ?').run(taskId);

  // Sync to iCloud after deleting task
  syncFromSQLiteToiCloud();

  return true;
});
ipcMain.handle('updateTask', (event, task) => {
  db.prepare('UPDATE tasks SET name = ?, date = ?, type = ?, notes = ? WHERE id = ?').run(task.name, task.date, task.type, task.notes || '', task.id);
  db.prepare('DELETE FROM task_tags WHERE task_id = ?').run(task.id);
  if (Array.isArray(task.tags)) {
    for (const tagName of task.tags) {
      let tagId;
      try {
        const tagInfo = db.prepare('INSERT INTO tags (name) VALUES (?)').run(tagName);
        tagId = tagInfo.lastInsertRowid;
      } catch {
        tagId = db.prepare('SELECT id FROM tags WHERE name = ?').get(tagName)?.id;
      }
      if (tagId) {
        db.prepare('INSERT OR IGNORE INTO task_tags (task_id, tag_id) VALUES (?, ?)').run(task.id, tagId);
      }
    }
  }

  // Sync to iCloud after updating task
  syncFromSQLiteToiCloud();

  return true;
});

// iCloud sync IPC handlers
ipcMain.handle('syncToiCloud', () => {
  return syncFromSQLiteToiCloud();
});

ipcMain.handle('syncFromiCloud', () => {
  return syncFromiCloudToSQLite();
});

ipcMain.handle('getSyncStatus', () => {
  const metadata = getSyncMetadata();
  return {
    iCloudAvailable: fs.existsSync(iCloudPath),
    lastSync: metadata?.lastSync || null,
    taskCount: metadata?.taskCount || 0,
    iCloudPath: iCloudPath
  };
});