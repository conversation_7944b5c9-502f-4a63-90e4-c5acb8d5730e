const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('api', {
  getTasks: () => ipcRenderer.invoke('getTasks'),
  addTask: (task) => ipcRenderer.invoke('addTask', task),
  deleteTask: (taskId) => ipcRenderer.invoke('deleteTask', taskId),
  updateTask: (task) => ipcRenderer.invoke('updateTask', task),
  // iCloud sync functions
  syncToiCloud: () => ipcRenderer.invoke('syncToiCloud'),
  syncFromiCloud: () => ipcRenderer.invoke('syncFromiCloud'),
  getSyncStatus: () => ipcRenderer.invoke('getSyncStatus'),
});
